'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { customerServiceAPI, type KnowledgeBase } from '@/lib/api/customer-service';
import { formatRelativeTime } from '@mtbrmg/shared';
import {
    ArrowLeft,
    BookOpen,
    Edit,
    Eye,
    Share2,
    Star,
    Tag,
    ThumbsDown,
    ThumbsUp,
    User
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

const KnowledgeBaseArticlePage = () => {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [article, setArticle] = useState<KnowledgeBase | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<KnowledgeBase[]>([]);

  useEffect(() => {
    if (params.id) {
      loadArticle();
    }
  }, [params.id]);

  const loadArticle = async () => {
    try {
      setLoading(true);
      
      // Load article details
      const articleResponse = await customerServiceAPI.getKnowledgeBaseArticle(Number(params.id));
      setArticle(articleResponse.data);
      
      // Increment view count
      await customerServiceAPI.incrementArticleView(Number(params.id));
      
      // Load related articles (same category)
      if (articleResponse.data.category) {
        const relatedResponse = await customerServiceAPI.getKnowledgeBase({
          category: articleResponse.data.category,
          page_size: 4
        });
        const related = (relatedResponse.data.results || relatedResponse.data)
          .filter((a: KnowledgeBase) => a.id !== Number(params.id));
        setRelatedArticles(related);
      }
      
    } catch (error) {
      console.error('Error loading article:', error);
      toast.error('فشل في تحميل المقال');
      router.push('/founder-dashboard/customer-service/knowledge-base');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkHelpful = async (isHelpful: boolean) => {
    if (!article) return;
    
    try {
      if (isHelpful) {
        await customerServiceAPI.markArticleHelpful(article.id);
        toast.success('شكراً لك! تم تسجيل تقييمك كمفيد');
      } else {
        await customerServiceAPI.markArticleNotHelpful(article.id);
        toast.success('شكراً لك! تم تسجيل تقييمك');
      }
      
      // Reload article to reflect changes
      loadArticle();
    } catch (error) {
      console.error('Error marking article:', error);
      toast.error('فشل في تسجيل التقييم');
    }
  };

  const getCategoryBadgeColor = (category: string) => {
    const colors: Record<string, string> = {
      general: 'bg-gray-100 text-gray-800',
      technical: 'bg-blue-100 text-blue-800',
      billing: 'bg-green-100 text-green-800',
      projects: 'bg-purple-100 text-purple-800',
      account: 'bg-orange-100 text-orange-800',
      troubleshooting: 'bg-red-100 text-red-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article?.title_ar,
          text: article?.content_ar.substring(0, 100) + '...',
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('تم نسخ رابط المقال');
    }
  };

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto py-12">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (!article) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto py-12 text-center">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">المقال غير موجود</h2>
          <p className="text-gray-600 mb-4">لم يتم العثور على المقال المطلوب</p>
          <Button onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base')}>
            العودة لقاعدة المعرفة
          </Button>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base')}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة لقاعدة المعرفة
          </Button>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="h-4 w-4 mr-2" />
              مشاركة
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/founder-dashboard/customer-service/knowledge-base/${article.id}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              تعديل
            </Button>
          </div>
        </div>

        {/* Article Content */}
        <Card>
          <CardHeader className="pb-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <Badge className={getCategoryBadgeColor(article.category)}>
                    {article.category_display}
                  </Badge>
                  {article.is_featured && (
                    <Badge className="bg-yellow-100 text-yellow-800">
                      <Star className="h-3 w-3 mr-1" />
                      مميز
                    </Badge>
                  )}
                  {!article.is_public && (
                    <Badge variant="outline" className="text-orange-600 border-orange-200">
                      خاص
                    </Badge>
                  )}
                </div>
                
                <CardTitle className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                  {article.title_ar}
                </CardTitle>
                
                {article.title_en && (
                  <p className="text-lg text-gray-600 mb-4">
                    {article.title_en}
                  </p>
                )}
                
                {/* Article Meta */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {article.created_by?.full_name || 'غير محدد'}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {article.view_count} مشاهدة
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsUp className="h-4 w-4" />
                    {article.helpful_count} مفيد
                  </div>
                  <span>
                    {formatRelativeTime(article.created_at)}
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                {article.content_ar}
              </div>
              
              {article.content_en && (
                <>
                  <Separator className="my-6" />
                  <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                    {article.content_en}
                  </div>
                </>
              )}
            </div>
            
            {/* Tags */}
            {article.tags.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">العلامات:</h4>
                <div className="flex flex-wrap gap-2">
                  {article.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-sm">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <Separator />
            
            {/* Helpfulness Rating */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-3">هل كان هذا المقال مفيداً؟</h4>
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => handleMarkHelpful(true)}
                  className="text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
                >
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  نعم، مفيد ({article.helpful_count})
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleMarkHelpful(false)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                >
                  <ThumbsDown className="h-4 w-4 mr-2" />
                  لا، غير مفيد ({article.not_helpful_count})
                </Button>
              </div>
              
              {(article.helpful_count + article.not_helpful_count) > 0 && (
                <div className="mt-3 text-sm text-gray-600">
                  معدل الفائدة: {(article.helpfulness_ratio * 100).toFixed(1)}%
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-semibold">مقالات ذات صلة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {relatedArticles.map((relatedArticle) => (
                  <Card
                    key={relatedArticle.id}
                    className="hover:shadow-md transition-shadow cursor-pointer group"
                    onClick={() => router.push(`/founder-dashboard/customer-service/knowledge-base/${relatedArticle.id}`)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-base group-hover:text-blue-600 transition-colors line-clamp-2">
                            {relatedArticle.title_ar}
                          </CardTitle>
                          {relatedArticle.title_en && (
                            <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                              {relatedArticle.title_en}
                            </p>
                          )}
                        </div>
                        {relatedArticle.is_featured && (
                          <Star className="h-4 w-4 text-yellow-500 flex-shrink-0 ml-2" />
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                        {relatedArticle.content_ar.substring(0, 100)}...
                      </p>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {relatedArticle.view_count}
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-3 w-3" />
                            {relatedArticle.helpful_count}
                          </div>
                        </div>
                        <span>{formatRelativeTime(relatedArticle.created_at)}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
};

export default KnowledgeBaseArticlePage;
