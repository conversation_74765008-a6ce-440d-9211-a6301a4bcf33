'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { customerServiceAPI } from '@/lib/api/customer-service';
import {
    ArrowLeft,
    BookOpen,
    Plus,
    RefreshCw,
    Save,
    Star,
    Tag,
    X
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface ArticleForm {
  title_ar: string;
  title_en: string;
  content_ar: string;
  content_en: string;
  category: string;
  tags: string[];
  is_public: boolean;
  is_featured: boolean;
}

interface FormErrors {
  title_ar?: string;
  content_ar?: string;
  category?: string;
}

const NewKnowledgeBaseArticlePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<ArticleForm>({
    title_ar: '',
    title_en: '',
    content_ar: '',
    content_en: '',
    category: '',
    tags: [],
    is_public: true,
    is_featured: false
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [newTag, setNewTag] = useState('');

  // Category options
  const categories = [
    { value: 'general', label: 'عام' },
    { value: 'technical', label: 'تقني' },
    { value: 'billing', label: 'الفواتير' },
    { value: 'projects', label: 'المشاريع' },
    { value: 'account', label: 'الحساب' },
    { value: 'troubleshooting', label: 'استكشاف الأخطاء' }
  ];

  const handleInputChange = (field: keyof ArticleForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !form.tags.includes(newTag.trim())) {
      setForm(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!form.title_ar.trim()) {
      newErrors.title_ar = 'العنوان بالعربية مطلوب';
    }

    if (!form.content_ar.trim()) {
      newErrors.content_ar = 'المحتوى بالعربية مطلوب';
    }

    if (!form.category) {
      newErrors.category = 'الفئة مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    try {
      setLoading(true);
      
      const articleData = {
        title_ar: form.title_ar.trim(),
        title_en: form.title_en.trim() || undefined,
        content_ar: form.content_ar.trim(),
        content_en: form.content_en.trim() || undefined,
        category: form.category,
        tags: form.tags,
        is_public: form.is_public,
        is_featured: form.is_featured
      };

      const response = await customerServiceAPI.createKnowledgeBaseArticle(articleData);
      
      toast.success('تم إنشاء المقال بنجاح');
      router.push(`/founder-dashboard/customer-service/knowledge-base/${response.data.id}`);
    } catch (error) {
      console.error('Error creating article:', error);
      toast.error('فشل في إنشاء المقال');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryBadgeColor = (category: string) => {
    const colors: Record<string, string> = {
      general: 'bg-gray-100 text-gray-800',
      technical: 'bg-blue-100 text-blue-800',
      billing: 'bg-green-100 text-green-800',
      projects: 'bg-purple-100 text-purple-800',
      account: 'bg-orange-100 text-orange-800',
      troubleshooting: 'bg-red-100 text-red-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة لقاعدة المعرفة
            </Button>
          </div>
        </div>

        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">إنشاء مقال جديد</h1>
          <p className="text-gray-600 mt-1">أضف مقال جديد إلى قاعدة المعرفة</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية للمقال
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Arabic Title */}
              <div className="space-y-2">
                <Label htmlFor="title_ar">العنوان بالعربية *</Label>
                <Input
                  id="title_ar"
                  placeholder="أدخل عنوان المقال بالعربية"
                  value={form.title_ar}
                  onChange={(e) => handleInputChange('title_ar', e.target.value)}
                  className={errors.title_ar ? 'border-red-500' : ''}
                />
                {errors.title_ar && (
                  <p className="text-sm text-red-600">{errors.title_ar}</p>
                )}
              </div>

              {/* English Title */}
              <div className="space-y-2">
                <Label htmlFor="title_en">العنوان بالإنجليزية (اختياري)</Label>
                <Input
                  id="title_en"
                  placeholder="Enter article title in English"
                  value={form.title_en}
                  onChange={(e) => handleInputChange('title_en', e.target.value)}
                />
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category">الفئة *</Label>
                <Select value={form.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر فئة المقال" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-600">{errors.category}</p>
                )}
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>العلامات</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="أضف علامة جديدة"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddTag}
                    disabled={!newTag.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                {form.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {form.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-sm">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 ml-1 hover:bg-transparent"
                          onClick={() => handleRemoveTag(tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          <Card>
            <CardHeader>
              <CardTitle>المحتوى</CardTitle>
              <CardDescription>
                اكتب محتوى المقال بالتفصيل
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Arabic Content */}
              <div className="space-y-2">
                <Label htmlFor="content_ar">المحتوى بالعربية *</Label>
                <Textarea
                  id="content_ar"
                  placeholder="اكتب محتوى المقال بالعربية..."
                  value={form.content_ar}
                  onChange={(e) => handleInputChange('content_ar', e.target.value)}
                  className={`min-h-[200px] ${errors.content_ar ? 'border-red-500' : ''}`}
                />
                {errors.content_ar && (
                  <p className="text-sm text-red-600">{errors.content_ar}</p>
                )}
              </div>

              {/* English Content */}
              <div className="space-y-2">
                <Label htmlFor="content_en">المحتوى بالإنجليزية (اختياري)</Label>
                <Textarea
                  id="content_en"
                  placeholder="Write article content in English..."
                  value={form.content_en}
                  onChange={(e) => handleInputChange('content_en', e.target.value)}
                  className="min-h-[200px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>إعدادات المقال</CardTitle>
              <CardDescription>
                تحديد إعدادات الرؤية والعرض للمقال
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Public/Private */}
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="is_public"
                  checked={form.is_public}
                  onCheckedChange={(checked) => handleInputChange('is_public', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="is_public"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    مقال عام
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    يمكن لجميع المستخدمين رؤية هذا المقال
                  </p>
                </div>
              </div>

              {/* Featured */}
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="is_featured"
                  checked={form.is_featured}
                  onCheckedChange={(checked) => handleInputChange('is_featured', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="is_featured"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    مقال مميز
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    سيظهر هذا المقال في قسم المقالات المميزة
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          {(form.title_ar || form.category) && (
            <Card>
              <CardHeader>
                <CardTitle>معاينة المقال</CardTitle>
                <CardDescription>
                  كيف سيظهر المقال في قاعدة المعرفة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg line-clamp-2">
                          {form.title_ar || 'عنوان المقال'}
                        </CardTitle>
                        {form.title_en && (
                          <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                            {form.title_en}
                          </p>
                        )}
                      </div>
                      {form.is_featured && (
                        <Star className="h-5 w-5 text-yellow-500 flex-shrink-0 ml-2" />
                      )}
                    </div>

                    <div className="flex items-center gap-2 mt-2">
                      {form.category && (
                        <Badge className={getCategoryBadgeColor(form.category)}>
                          {categories.find(c => c.value === form.category)?.label}
                        </Badge>
                      )}
                      {!form.is_public && (
                        <Badge variant="outline" className="text-orange-600 border-orange-200">
                          خاص
                        </Badge>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                      {form.content_ar ? form.content_ar.substring(0, 150) + '...' : 'محتوى المقال سيظهر هنا...'}
                    </p>

                    {form.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {form.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                        {form.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{form.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <span>0 مشاهدة</span>
                        <span>0 تقييم مفيد</span>
                      </div>
                      <span>الآن</span>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          )}

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base')}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              إنشاء المقال
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
};

export default NewKnowledgeBaseArticlePage;
