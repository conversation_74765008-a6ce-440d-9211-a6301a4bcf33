'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { customerServiceAPI, type KnowledgeBase } from '@/lib/api/customer-service';
import { formatRelativeTime } from '@mtbrmg/shared';
import {
    ArrowLeft,
    BookOpen,
    Eye,
    Filter,
    Plus,
    RefreshCw,
    Search,
    Star,
    Tag,
    ThumbsDown,
    ThumbsUp,
    TrendingUp
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface KnowledgeBaseStats {
  total_articles: number;
  public_articles: number;
  featured_articles: number;
  total_views: number;
  avg_helpfulness: number;
  popular_categories: Array<{ category: string; count: number }>;
}

const KnowledgeBasePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [articles, setArticles] = useState<KnowledgeBase[]>([]);
  const [featuredArticles, setFeaturedArticles] = useState<KnowledgeBase[]>([]);
  const [popularArticles, setPopularArticles] = useState<KnowledgeBase[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [stats, setStats] = useState<KnowledgeBaseStats | null>(null);

  // Category options
  const categories = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'general', label: 'عام' },
    { value: 'technical', label: 'تقني' },
    { value: 'billing', label: 'الفواتير' },
    { value: 'projects', label: 'المشاريع' },
    { value: 'account', label: 'الحساب' },
    { value: 'troubleshooting', label: 'استكشاف الأخطاء' }
  ];

  // Sort options
  const sortOptions = [
    { value: 'created_at', label: 'الأحدث' },
    { value: '-created_at', label: 'الأقدم' },
    { value: '-view_count', label: 'الأكثر مشاهدة' },
    { value: '-helpful_count', label: 'الأكثر فائدة' },
    { value: 'title_ar', label: 'العنوان (أ-ي)' },
    { value: '-title_ar', label: 'العنوان (ي-أ)' }
  ];

  useEffect(() => {
    loadKnowledgeBaseData();
  }, [searchQuery, categoryFilter, sortBy]);

  const loadKnowledgeBaseData = async () => {
    try {
      setLoading(true);
      
      // Build query parameters
      const params: any = {
        ordering: sortBy
      };
      
      if (searchQuery) {
        params.search = searchQuery;
      }
      
      if (categoryFilter !== 'all') {
        params.category = categoryFilter;
      }

      // Load articles and featured/popular articles in parallel
      const [articlesResponse, featuredResponse, popularResponse] = await Promise.all([
        customerServiceAPI.getKnowledgeBase(params),
        customerServiceAPI.getFeaturedArticles(),
        customerServiceAPI.getPopularArticles()
      ]);
      
      setArticles(articlesResponse.data.results || articlesResponse.data);
      setFeaturedArticles(featuredResponse.data.results || featuredResponse.data);
      setPopularArticles(popularResponse.data.results || popularResponse.data);
      
      // Calculate stats from the data
      const allArticles = articlesResponse.data.results || articlesResponse.data;
      const calculatedStats: KnowledgeBaseStats = {
        total_articles: allArticles.length,
        public_articles: allArticles.filter((a: KnowledgeBase) => a.is_public).length,
        featured_articles: allArticles.filter((a: KnowledgeBase) => a.is_featured).length,
        total_views: allArticles.reduce((sum: number, a: KnowledgeBase) => sum + a.view_count, 0),
        avg_helpfulness: allArticles.length > 0 
          ? allArticles.reduce((sum: number, a: KnowledgeBase) => sum + a.helpfulness_ratio, 0) / allArticles.length 
          : 0,
        popular_categories: categories.slice(1).map(cat => ({
          category: cat.label,
          count: allArticles.filter((a: KnowledgeBase) => a.category === cat.value).length
        })).filter(cat => cat.count > 0)
      };
      setStats(calculatedStats);
      
    } catch (error) {
      console.error('Error loading knowledge base data:', error);
      toast.error('فشل في تحميل بيانات قاعدة المعرفة');
    } finally {
      setLoading(false);
    }
  };

  const handleArticleClick = (article: KnowledgeBase) => {
    // Increment view count
    customerServiceAPI.incrementArticleView(article.id).catch(console.error);
    
    // Navigate to article detail (we'll create this route later)
    router.push(`/founder-dashboard/customer-service/knowledge-base/${article.id}`);
  };

  const handleMarkHelpful = async (articleId: number, isHelpful: boolean) => {
    try {
      if (isHelpful) {
        await customerServiceAPI.markArticleHelpful(articleId);
        toast.success('تم تسجيل تقييمك كمفيد');
      } else {
        await customerServiceAPI.markArticleNotHelpful(articleId);
        toast.success('تم تسجيل تقييمك كغير مفيد');
      }
      
      // Reload data to reflect changes
      loadKnowledgeBaseData();
    } catch (error) {
      console.error('Error marking article:', error);
      toast.error('فشل في تسجيل التقييم');
    }
  };

  const getCategoryBadgeColor = (category: string) => {
    const colors: Record<string, string> = {
      general: 'bg-gray-100 text-gray-800',
      technical: 'bg-blue-100 text-blue-800',
      billing: 'bg-green-100 text-green-800',
      projects: 'bg-purple-100 text-purple-800',
      account: 'bg-orange-100 text-orange-800',
      troubleshooting: 'bg-red-100 text-red-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const filteredArticles = articles.filter(article => {
    const matchesSearch = !searchQuery || 
      article.title_ar.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.content_ar.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || article.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section - Following VST and GRS patterns */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/founder-dashboard/customer-service')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة لخدمة العملاء
            </Button>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button 
              onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base/new')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              مقال جديد
            </Button>
          </div>
        </div>

        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">قاعدة المعرفة</h1>
          <p className="text-gray-600 mt-1">إدارة مقالات المساعدة والدعم الفني</p>
        </div>

        {/* Stats Cards - CPR Implementation */}
        {stats && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي المقالات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total_articles}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المقالات المميزة</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.featured_articles}</p>
                  </div>
                  <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Star className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي المشاهدات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total_views.toLocaleString()}</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Eye className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">متوسط التقييم</p>
                    <p className="text-2xl font-bold text-gray-900">{(stats.avg_helpfulness * 100).toFixed(1)}%</p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters - ULM Implementation */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المقالات..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="ترتيب حسب" />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={loadKnowledgeBaseData}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs - SMA Implementation */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">جميع المقالات</TabsTrigger>
            <TabsTrigger value="featured">المقالات المميزة</TabsTrigger>
            <TabsTrigger value="popular">الأكثر شعبية</TabsTrigger>
            <TabsTrigger value="categories">الفئات</TabsTrigger>
          </TabsList>

          {/* All Articles Tab */}
          <TabsContent value="all">
            <div className="space-y-4">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : filteredArticles.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مقالات</h3>
                    <p className="text-gray-600 mb-4">
                      {searchQuery || categoryFilter !== 'all'
                        ? 'لم يتم العثور على مقالات تطابق معايير البحث'
                        : 'لم يتم إنشاء أي مقالات بعد'
                      }
                    </p>
                    <Button
                      onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base/new')}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      إنشاء مقال جديد
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredArticles.map((article) => (
                    <Card
                      key={article.id}
                      className="hover:shadow-lg transition-shadow cursor-pointer group"
                      onClick={() => handleArticleClick(article)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors line-clamp-2">
                              {article.title_ar}
                            </CardTitle>
                            {article.title_en && (
                              <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                                {article.title_en}
                              </p>
                            )}
                          </div>
                          {article.is_featured && (
                            <Star className="h-5 w-5 text-yellow-500 flex-shrink-0 ml-2" />
                          )}
                        </div>

                        <div className="flex items-center gap-2 mt-2">
                          <Badge className={getCategoryBadgeColor(article.category)}>
                            {article.category_display}
                          </Badge>
                          {!article.is_public && (
                            <Badge variant="outline" className="text-orange-600 border-orange-200">
                              خاص
                            </Badge>
                          )}
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                          {article.content_ar.substring(0, 150)}...
                        </p>

                        {/* Tags */}
                        {article.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-4">
                            {article.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                            {article.tags.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{article.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Stats and Actions */}
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {article.view_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsUp className="h-4 w-4" />
                              {article.helpful_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsDown className="h-4 w-4" />
                              {article.not_helpful_count}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkHelpful(article.id, true);
                              }}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                            >
                              <ThumbsUp className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkHelpful(article.id, false);
                              }}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <ThumbsDown className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>
                              {article.created_by?.full_name || 'غير محدد'}
                            </span>
                            <span>
                              {formatRelativeTime(article.created_at)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Featured Articles Tab */}
          <TabsContent value="featured">
            <div className="space-y-4">
              {featuredArticles.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مقالات مميزة</h3>
                    <p className="text-gray-600">لم يتم تمييز أي مقالات بعد</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredArticles.map((article) => (
                    <Card
                      key={article.id}
                      className="hover:shadow-lg transition-shadow cursor-pointer group border-yellow-200"
                      onClick={() => handleArticleClick(article)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors line-clamp-2">
                              {article.title_ar}
                            </CardTitle>
                            {article.title_en && (
                              <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                                {article.title_en}
                              </p>
                            )}
                          </div>
                          <Star className="h-5 w-5 text-yellow-500 flex-shrink-0 ml-2" />
                        </div>

                        <Badge className={getCategoryBadgeColor(article.category)}>
                          {article.category_display}
                        </Badge>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                          {article.content_ar.substring(0, 150)}...
                        </p>

                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {article.view_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsUp className="h-4 w-4" />
                              {article.helpful_count}
                            </div>
                          </div>
                          <span>{formatRelativeTime(article.created_at)}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Popular Articles Tab */}
          <TabsContent value="popular">
            <div className="space-y-4">
              {popularArticles.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مقالات شائعة</h3>
                    <p className="text-gray-600">لم يتم تسجيل مشاهدات كافية بعد</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {popularArticles.map((article) => (
                    <Card
                      key={article.id}
                      className="hover:shadow-lg transition-shadow cursor-pointer group border-green-200"
                      onClick={() => handleArticleClick(article)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors line-clamp-2">
                              {article.title_ar}
                            </CardTitle>
                            {article.title_en && (
                              <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                                {article.title_en}
                              </p>
                            )}
                          </div>
                          <TrendingUp className="h-5 w-5 text-green-500 flex-shrink-0 ml-2" />
                        </div>

                        <Badge className={getCategoryBadgeColor(article.category)}>
                          {article.category_display}
                        </Badge>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                          {article.content_ar.substring(0, 150)}...
                        </p>

                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {article.view_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsUp className="h-4 w-4" />
                              {article.helpful_count}
                            </div>
                          </div>
                          <span>{formatRelativeTime(article.created_at)}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories">
            <div className="space-y-4">
              {stats && stats.popular_categories.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {stats.popular_categories.map((categoryData, index) => (
                    <Card
                      key={index}
                      className="hover:shadow-lg transition-shadow cursor-pointer group"
                      onClick={() => {
                        const categoryValue = categories.find(c => c.label === categoryData.category)?.value;
                        if (categoryValue) {
                          setCategoryFilter(categoryValue);
                          // Switch to all articles tab to show filtered results
                          const allTab = document.querySelector('[data-state="active"][value="all"]') as HTMLElement;
                          if (allTab) allTab.click();
                        }
                      }}
                    >
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                          {categoryData.category}
                        </CardTitle>
                        <CardDescription>
                          {categoryData.count} مقال
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                            <span className="text-sm text-gray-600">
                              {((categoryData.count / stats.total_articles) * 100).toFixed(1)}% من المجموع
                            </span>
                          </div>
                          <Button variant="ghost" size="sm" className="group-hover:bg-blue-50">
                            عرض المقالات
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد فئات</h3>
                    <p className="text-gray-600">لم يتم إنشاء مقالات في أي فئة بعد</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </UnifiedLayout>
  );
};

export default KnowledgeBasePage;
